"""
应用生命周期管理
处理应用启动和关闭时的初始化和清理工作
"""

import os
import shutil
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Any

# 避免直接导入FastAPI，防止Pydantic版本冲突
# from fastapi import FastAPI
from loguru import logger

from core.config import settings


async def startup_directories():
    """创建必要的存储目录"""
    try:
        logger.info("📁 Creating storage directories...")

        directories = [
            "static/videos",
            "static/bp",
            "static/videos/temp",
            "logs/app",
            "logs/celery"
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            logger.debug(f"Created directory: {directory}")

        logger.success("✅ Storage directories created")
    except Exception as e:
        logger.error(f"❌ Directory creation failed: {e}")
        raise


async def startup_validation():
    """验证应用配置"""
    try:
        logger.info("🔍 Validating application configuration...")

        # 检查必要的配置
        required_configs = {
            "SECRET_KEY": settings.SECRET_KEY,
        }

        missing_configs = []
        for name, value in required_configs.items():
            if not value:
                missing_configs.append(name)

        if missing_configs:
            raise ValueError(f"Missing required configurations: {', '.join(missing_configs)}")

        logger.success("✅ Configuration validation passed")
    except Exception as e:
        logger.error(f"❌ Configuration validation failed: {e}")
        raise


async def shutdown_cleanup():
    """清理临时文件"""
    try:
        logger.info("🧹 Cleaning up temporary files...")

        temp_dir = "static/videos/temp"
        if os.path.exists(temp_dir):
            for item in os.listdir(temp_dir):
                item_path = os.path.join(temp_dir, item)
                if os.path.isdir(item_path):
                    shutil.rmtree(item_path)
                    logger.debug(f"Removed temp directory: {item_path}")

        logger.success("✅ Temporary files cleaned up")
    except Exception as e:
        logger.error(f"❌ Cleanup error: {e}")


@asynccontextmanager
async def lifespan(app: Any) -> AsyncGenerator[None, None]:
    """
    应用生命周期管理

    Args:
        app: FastAPI应用实例

    Yields:
        None: 应用运行期间
    """
    # ========== 启动阶段 ==========
    logger.info("🚀 Starting Video2BP FastAPI application...")

    try:
        # 1. 验证配置
        await startup_validation()

        # 2. 创建目录
        await startup_directories()

        logger.success("🎉 Application startup completed successfully!")

    except Exception as e:
        logger.error(f"💥 Application startup failed: {e}")
        raise

    # ========== 运行阶段 ==========
    yield

    # ========== 关闭阶段 ==========
    logger.info("🛑 Shutting down Video2BP FastAPI application...")

    try:
        # 1. 清理临时文件
        await shutdown_cleanup()

        logger.success("👋 Application shutdown completed successfully!")

    except Exception as e:
        logger.error(f"💥 Application shutdown error: {e}")
        # 关闭阶段的错误不应该抛出，只记录日志