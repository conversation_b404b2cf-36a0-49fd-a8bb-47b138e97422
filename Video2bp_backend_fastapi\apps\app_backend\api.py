import datetime
from typing import List
from fastapi import APIRouter, Body, Depends, Path
from sqlalchemy import func, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import HTTP_404_NOT_FOUND

from apps.app_backend import doc
from apps.app_backend.schema import (
    BackendCreateRequest,
    BackendCreateResponse,
    BackendCreateResponseModel,
    BackendDeleteResponse,
    BackendDeleteResponseModel,
    BackendInfoResponse,
    BackendInfoResponseModel,
    BackendListQueryRequest,
    BackendListResponse,
    BackendListResponseModel,
    BackendUpdateRequest,
    BackendUpdateResponse,
    BackendUpdateResponseModel,
    BackendsDeleteResponse,
    BackendsDeleteResponseModel,
    BackendsPatchRequest,
    BackendsPatchResponse,
    BackendsPatchResponseModel,
)
from apps.app_backend.model import Backend
from core.e import ErrorCode, ErrorMessage
from db.database import get_async_db
from schemas.base import OrderType
from schemas.response import PaginationResponse


router = APIRouter()


"""
接口：Backend 表增删改查

GET    /api/backends               ->  get_backends    ->  获取所有 backend
POST   /api/backends               ->  add_backend     ->  创建单个 backend
PATCH  /api/backends               ->  patch_backends  ->  批量更新 backend
DELETE /api/backends               ->  delete_backends ->  批量注销 backend
GET    /api/backends/{backend_id}  ->  get_backend     ->  获取单个 backend
PUT    /api/backends/{backend_id}  ->  update_backend  ->  更新单个 backend
DELETE /api/backends/{backend_id}  ->  delete_backend  ->  注销单个 backend
"""


@router.get(
    "",
    name="获取所有 backend",
    response_model=BackendListResponseModel,
    responses=doc.get_backends_responses,
)
async def get_backends(
    query_params: BackendListQueryRequest = Depends(),
    db: AsyncSession = Depends(get_async_db),
):
    # 获取总数
    total_count = (await db.execute(select(func.count()).select_from(Backend))).scalar()

    # 查询
    stmt = await Backend.query()
    if query_params.size is not None:
        offset = (query_params.page - 1) * query_params.size
        stmt = stmt.offset(offset).limit(query_params.size)
    if query_params.order_type:
        stmt = stmt.order_by(
            getattr(Backend, query_params.order_by).desc()
            if query_params.order_type == OrderType.DESC
            else getattr(Backend, query_params.order_by).asc()
        )

    db_backends: List[Backend] = (await db.execute(stmt)).scalars().all()

    return BackendListResponseModel(
        data=PaginationResponse(
            list=[
                BackendListResponse.model_validate(
                    db_backend, from_attributes=True
                ).model_dump()
                for db_backend in db_backends
            ],
            count=len(db_backends),
            page=query_params.page,
            size=query_params.size,
            total=total_count,
        ).model_dump()
    )


@router.post(
    "",
    name="创建单个 backend",
    response_model=BackendCreateResponseModel,
    responses=doc.create_backend_responses,
)
async def create_backend(
    backend: BackendCreateRequest = Body(
        ..., openapi_examples=doc.create_backend_request
    ),
    db: AsyncSession = Depends(get_async_db),
):
    async with db.begin():
        db_backend: Backend = await Backend.create(db, **backend.model_dump())
    return BackendCreateResponseModel(
        data=BackendCreateResponse.model_validate(db_backend, from_attributes=True)
    )


@router.patch(
    "",
    name="批量更新 backend",
    response_model=BackendsPatchResponseModel,
    responses=doc.patch_backends_responses,
)
async def patch_backends(
    backends_patch_request: BackendsPatchRequest = Body(
        ..., openapi_examples=doc.patch_backends_request
    ),
    db: AsyncSession = Depends(get_async_db),
):
    async with db.begin():
        stmt = (await Backend.query()).filter(
            Backend.id.in_(backends_patch_request.ids)
        )
        db_backends: List[Backend] = (await db.execute(stmt)).scalars().all()
        for db_backend in db_backends:
            db_backend.name = backends_patch_request.name
        db.flush()
    return BackendsPatchResponseModel(
        data=BackendsPatchResponse(
            ids=[db_backend.id for db_backend in db_backends],
            name=backends_patch_request.name,
        )
    )


@router.delete(
    "",
    name="批量注销 backend",
    response_model=BackendsDeleteResponseModel,
    responses=doc.delete_backends_responses,
)
async def delete_backends(
    ids: List[int] = Body(
        ...,
        description="backend id 列表",
        embed=True,
        json_schema_extra=doc.delete_backends_request,
    ),
    db: AsyncSession = Depends(get_async_db),
):
    async with db.begin():
        stmt_select = (await Backend.query()).filter(Backend.id.in_(ids))
        db_backends: List[Backend] = (await db.execute(stmt_select)).scalars().all()

        stmt_update = (
            update(Backend)
            .where(Backend.deleted_at.is_(None))
            .filter(Backend.id.in_(ids))
            .values(deleted_at=datetime.datetime.now())
        )
        await db.execute(stmt_update)
    return BackendsDeleteResponseModel(
        data=BackendsDeleteResponse(
            ids=[db_backend.id for db_backend in db_backends]
        )
    )


@router.get(
    "/{backend_id}",
    name="获取单个 backend by id",
    response_model=BackendInfoResponseModel,
    responses=doc.get_backend_by_id_responses,
)
async def get_backend_by_id(
    backend_id: int = Path(..., description="backend id", ge=1, example=1),
    db: AsyncSession = Depends(get_async_db),
):
    db_backend: Backend | None = await Backend.get_by(db, id=backend_id)
    if db_backend is None:
        return BackendInfoResponseModel(
            code=ErrorCode.NOT_FOUND,
            message=ErrorMessage.get(ErrorCode.NOT_FOUND),
        ).to_json(status_code=HTTP_404_NOT_FOUND)

    return BackendInfoResponseModel(
        data=BackendInfoResponse.model_validate(db_backend, from_attributes=True)
    )


@router.put(
    "/{backend_id}",
    name="更新单个 backend by id",
    response_model=BackendUpdateResponseModel,
    responses=doc.update_backend_by_id_responses,
)
async def update_backend_by_id(
    backend_id: int = Path(..., description="backend id", ge=1),
    backend_update_request: BackendUpdateRequest = Body(
        ..., openapi_examples=doc.update_backend_by_id_request
    ),
    db: AsyncSession = Depends(get_async_db),
):
    async with db.begin():
        db_backend: Backend | None = await Backend.get_by(db, id=backend_id)
        if db_backend is None:
            return BackendUpdateResponseModel(
                code=ErrorCode.NOT_FOUND,
                message=ErrorMessage.get(ErrorCode.NOT_FOUND),
            )

        # 更新 name
        if backend_update_request.name is not None:
            db_backend.username = backend_update_request.name

        await db_backend.save(db)

    return BackendUpdateResponseModel(
        data=BackendUpdateResponse.model_validate(
            db_backend, from_attributes=True
        ).model_dump(),
    )


@router.delete(
    "/{backend_id}",
    name="注销单个 backend by id",
    response_model=BackendDeleteResponseModel,
    responses=doc.delete_backend_by_id_responses,
)
async def delete_backend_by_id(
    backend_id: int = Path(..., description="backend id", ge=1),
    db: AsyncSession = Depends(get_async_db),
):
    async with db.begin():
        db_backend: Backend | None = await Backend.get_by(db, id=backend_id)
        if db_backend is None:
            return BackendDeleteResponseModel(
                code=ErrorCode.NOT_FOUND,
                message=ErrorMessage.get(ErrorCode.NOT_FOUND),
            ).to_json(status_code=HTTP_404_NOT_FOUND)
        await db_backend.remove(db)

    return BackendDeleteResponseModel(data=BackendDeleteResponse(id=db_backend.id))
