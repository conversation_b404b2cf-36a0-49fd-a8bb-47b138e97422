from typing import Optional
from pydantic import BaseModel, EmailStr, Field
from schemas.base import BaseSchema
from schemas.response import StandardResponse


class SendVerificationCodeRequest(BaseSchema):
    """发送验证码请求"""
    email: EmailStr = Field(..., description="邮箱地址")


class SendVerificationCodeResponse(BaseSchema):
    """发送验证码响应"""
    pass


class SendVerificationCodeResponseModel(StandardResponse):
    """发送验证码响应模型"""
    data: Optional[SendVerificationCodeResponse] = None


class UserRegisterRequest(BaseSchema):
    """用户注册请求"""
    email: EmailStr = Field(..., description="邮箱地址")
    password: str = Field(..., min_length=8, max_length=32, description="密码")
    code: str = Field(..., description="验证码")


class UserRegisterResponse(BaseSchema):
    """用户注册响应"""
    pass


class UserRegisterResponseModel(StandardResponse):
    """用户注册响应模型"""
    data: Optional[UserRegisterResponse] = None


class UserLoginRequest(BaseSchema):
    """用户登录请求"""
    email: EmailStr = Field(..., description="邮箱地址")
    password: str = Field(..., description="密码")


class UserLoginResponse(BaseSchema):
    """用户登录响应"""
    pass


class UserLoginResponseModel(StandardResponse):
    """用户登录响应模型"""
    data: Optional[UserLoginResponse] = None


class UserLogoutResponse(BaseSchema):
    """用户登出响应"""
    pass


class UserLogoutResponseModel(StandardResponse):
    """用户登出响应模型"""
    data: Optional[UserLogoutResponse] = None


class UserInfoRequest(BaseSchema):
    """获取用户信息请求"""
    userId: int = Field(..., description="用户ID")


class UserInfoResponse(BaseSchema):
    """用户信息响应"""
    id: int = Field(..., description="用户ID")
    username: Optional[str] = Field(None, description="用户名")
    email: str = Field(..., description="邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    status: int = Field(..., description="状态")
    vipLevel: int = Field(..., description="VIP等级", alias="vip_level")


class UserInfoResponseModel(StandardResponse):
    """用户信息响应模型"""
    data: Optional[UserInfoResponse] = None
