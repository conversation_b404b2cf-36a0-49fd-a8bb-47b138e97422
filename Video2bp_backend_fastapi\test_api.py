#!/usr/bin/env python3
"""
API测试脚本
用于测试迁移后的FastAPI接口
"""

import requests
import json
from typing import Dict, Any


class APITester:
    def __init__(self, base_url: str = "http://localhost:8000/api"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_monitor(self) -> bool:
        """测试系统监控接口"""
        try:
            response = self.session.get(f"{self.base_url}/monitor")
            print(f"Monitor API: {response.status_code} - {response.json()}")
            return response.status_code == 200
        except Exception as e:
            print(f"Monitor API failed: {e}")
            return False
    
    def test_company_info(self) -> bool:
        """测试公司信息接口"""
        try:
            response = self.session.get(f"{self.base_url}/compony/info")
            print(f"Company Info API: {response.status_code} - {response.json()}")
            return response.status_code == 200
        except Exception as e:
            print(f"Company Info API failed: {e}")
            return False
    
    def test_combo_info(self) -> bool:
        """测试套餐信息接口"""
        try:
            response = self.session.get(f"{self.base_url}/combo/info")
            print(f"Combo Info API: {response.status_code} - {response.json()}")
            return response.status_code == 200
        except Exception as e:
            print(f"Combo Info API failed: {e}")
            return False
    
    def test_send_verification_code(self, email: str = "<EMAIL>") -> bool:
        """测试发送验证码接口"""
        try:
            data = {"email": email}
            response = self.session.post(
                f"{self.base_url}/user/send_verification_code",
                json=data
            )
            print(f"Send Verification Code API: {response.status_code} - {response.json()}")
            return response.status_code == 200
        except Exception as e:
            print(f"Send Verification Code API failed: {e}")
            return False
    
    def test_user_register(self, email: str = "<EMAIL>", 
                          password: str = "testpassword", code: str = "123456") -> bool:
        """测试用户注册接口"""
        try:
            data = {
                "email": email,
                "password": password,
                "code": code
            }
            response = self.session.post(
                f"{self.base_url}/user/register",
                json=data
            )
            print(f"User Register API: {response.status_code} - {response.json()}")
            return response.status_code == 200
        except Exception as e:
            print(f"User Register API failed: {e}")
            return False
    
    def test_user_login(self, email: str = "<EMAIL>", 
                       password: str = "testpassword") -> bool:
        """测试用户登录接口"""
        try:
            data = {
                "email": email,
                "password": password
            }
            response = self.session.post(
                f"{self.base_url}/user/login",
                json=data
            )
            print(f"User Login API: {response.status_code} - {response.json()}")
            return response.status_code == 200
        except Exception as e:
            print(f"User Login API failed: {e}")
            return False
    
    def test_news_list(self) -> bool:
        """测试新闻列表接口"""
        try:
            data = {
                "page": 1,
                "size": 10
            }
            response = self.session.post(
                f"{self.base_url}/news/list",
                json=data
            )
            print(f"News List API: {response.status_code} - {response.json()}")
            return response.status_code == 200
        except Exception as e:
            print(f"News List API failed: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始API测试...")
        print("=" * 50)
        
        tests = [
            ("系统监控", self.test_monitor),
            ("公司信息", self.test_company_info),
            ("套餐信息", self.test_combo_info),
            ("发送验证码", self.test_send_verification_code),
            ("用户注册", self.test_user_register),
            ("用户登录", self.test_user_login),
            ("新闻列表", self.test_news_list),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n测试 {test_name}:")
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        
        print("\n" + "=" * 50)
        print(f"测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过!")
        else:
            print("⚠️  部分测试失败，请检查API实现")


if __name__ == "__main__":
    tester = APITester()
    tester.run_all_tests()
