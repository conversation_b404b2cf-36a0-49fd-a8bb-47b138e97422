from fastapi import APIRouter, Depends, File, Form, HTTPException, Query, UploadFile
from fastapi.responses import FileResponse, StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import HTTP_400_BAD_REQUEST

from apps.app_video_manage.schema import (
    VideoUploadResponseModel,
    VideoListRequest,
    VideoListResponseModel,
    ChunkUploadResponseModel,
    MergeChunksResponseModel,
    UploadStatusResponseModel,
)
from apps.app_video_manage.service import video_manage_service
from core.e import ErrorCode, ErrorMessage
from db.database import get_async_db
from lib.cookie_auth import get_current_user_from_cookie
from apps.app_user.model import User
from schemas.response import StandardResponse


router = APIRouter()


@router.post(
    "/upload",
    name="视频上传",
    response_model=VideoUploadResponseModel,
)
async def video_upload(
    fileData: UploadFile = File(...),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """视频上传"""
    try:
        result = await video_manage_service.upload_video(db, current_user.id, fileData)
        return VideoUploadResponseModel(data=result)
    except HTTPException as e:
        return VideoUploadResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return VideoUploadResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.post(
    "/list",
    name="视频列表查询",
    response_model=VideoListResponseModel,
)
async def video_list(
    request: VideoListRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """获取用户视频列表"""
    try:
        result = await video_manage_service.get_video_list(
            db, current_user.id, request.startPage, request.pageSize
        )
        return VideoListResponseModel(data=result)
    except Exception as e:
        return VideoListResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.get(
    "/download",
    name="视频下载",
)
async def video_download(
    videoId: int = Query(..., description="视频ID"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """视频下载"""
    try:
        file_path = await video_manage_service.get_video_path(db, current_user.id, videoId)
        return FileResponse(
            path=file_path,
            media_type='application/octet-stream',
            filename=f"video_{videoId}.mp4"
        )
    except HTTPException as e:
        return StandardResponse(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)


@router.get(
    "/bp/download",
    name="视频骨骼点下载",
)
async def bp_download(
    videoId: int = Query(..., description="视频ID"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """骨骼点文件下载"""
    try:
        file_path = await video_manage_service.get_bp_path(db, current_user.id, videoId)
        return FileResponse(
            path=file_path,
            media_type='application/json',
            filename=f"video_{videoId}_bp.json"
        )
    except HTTPException as e:
        return StandardResponse(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)


@router.get(
    "/download/all",
    name="打包下载",
)
async def download_all(
    videoId: int = Query(..., description="视频ID"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """打包下载视频和骨骼点文件"""
    try:
        memory_file = await video_manage_service.get_video_all(db, current_user.id, videoId)

        def iterfile():
            yield from memory_file

        return StreamingResponse(
            iterfile(),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename=video_{videoId}_package.zip"}
        )
    except HTTPException as e:
        return StandardResponse(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)


@router.post(
    "/upload_chunk",
    name="分片上传",
    response_model=ChunkUploadResponseModel,
)
async def upload_chunk(
    chunk: UploadFile = File(...),
    chunkNumber: int = Form(...),
    totalChunks: int = Form(...),
    fileId: str = Form(...),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """分片上传"""
    try:
        result = await video_manage_service.save_chunk(
            current_user.id, fileId, chunk, chunkNumber, totalChunks
        )
        return ChunkUploadResponseModel(data=result)
    except Exception as e:
        return ChunkUploadResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.post(
    "/merge_chunks",
    name="合并分片",
    response_model=MergeChunksResponseModel,
)
async def merge_chunks(
    fileId: str = Form(...),
    filename: str = Form(...),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """合并分片"""
    try:
        result = await video_manage_service.merge_chunks(
            db, current_user.id, fileId, filename
        )
        return MergeChunksResponseModel(data=result)
    except HTTPException as e:
        return MergeChunksResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return MergeChunksResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.get(
    "/upload/status",
    name="上传进度查询",
    response_model=UploadStatusResponseModel,
)
async def video_upload_status(
    fileId: str = Query(..., description="文件ID"),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """查询上传状态"""
    try:
        result = await video_manage_service.get_upload_status(current_user.id, fileId)
        return UploadStatusResponseModel(data=result)
    except Exception as e:
        return UploadStatusResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)
