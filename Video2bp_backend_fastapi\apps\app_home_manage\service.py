from typing import Dict, Any, List
from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import desc, func
from starlette.status import HTTP_404_NOT_FOUND

from apps.app_home_manage.model import News
from core.e import ErrorCode, ErrorMessage


class HomeManageService:
    """首页管理服务"""
    
    def get_company_info(self) -> Dict[str, Any]:
        """获取公司信息"""
        # 这里返回静态的公司信息，实际项目中可能从数据库或配置文件读取
        return {
            "name": "灵宇科技",
            "description": "专业的AI动作捕捉解决方案提供商",
            "address": "北京市海淀区中关村科技园",
            "phone": "************",
            "email": "<EMAIL>"
        }
    
    def get_combo_info(self) -> Dict[str, Any]:
        """获取套餐信息"""
        # 这里返回静态的套餐信息，实际项目中可能从数据库读取
        combos = [
            {
                "id": 1,
                "name": "基础版",
                "price": 99.0,
                "description": "适合个人用户的基础功能",
                "features": ["基础动作捕捉", "标准输出格式", "在线支持"]
            },
            {
                "id": 2,
                "name": "专业版",
                "price": 299.0,
                "description": "适合专业用户的高级功能",
                "features": ["高精度动作捕捉", "多种输出格式", "优先技术支持", "批量处理"]
            },
            {
                "id": 3,
                "name": "企业版",
                "price": 999.0,
                "description": "适合企业用户的完整解决方案",
                "features": ["企业级动作捕捉", "定制化输出", "专属技术支持", "API接入", "私有化部署"]
            }
        ]
        return {"combos": combos}
    
    async def get_news_list(self, db: AsyncSession, page: int = 1, size: int = 9, 
                           news_type: int = None) -> Dict[str, Any]:
        """获取新闻列表"""
        # 构建查询条件
        query = select(News).filter(News.is_pinned == True)
        
        if news_type is not None:
            query = query.filter(News.type == news_type)
        
        # 计算总数
        count_query = select(func.count()).select_from(News).filter(News.is_pinned == True)
        if news_type is not None:
            count_query = count_query.filter(News.type == news_type)
        
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 获取分页数据
        offset = (page - 1) * size
        query = query.order_by(
            desc(News.is_pinned),
            desc(News.created_at)
        ).offset(offset).limit(size)
        
        result = await db.execute(query)
        news_items = result.scalars().all()
        
        news_list = []
        for news in news_items:
            news_list.append({
                "id": news.id,
                "title": news.title,
                "cover": news.cover,
                "author": news.author,
                "content": news.content,
                "excerpt": news.excerpt,
                "createdAt": news.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "isPinned": news.is_pinned,
                "profilePicture": news.profile_picture,
                "tag": news.tag,
                "type": news.type,
            })
        
        return {
            "total": total,
            "newsList": news_list
        }
    
    async def get_news_detail(self, db: AsyncSession, news_id: int) -> Dict[str, Any]:
        """获取新闻详情"""
        result = await db.execute(select(News).filter(News.id == news_id))
        news = result.scalars().first()
        
        if not news:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"{ErrorCode.NOT_FOUND}_{ErrorMessage.get(ErrorCode.NOT_FOUND)}"
            )
        
        return {
            "id": news.id,
            "title": news.title,
            "cover": news.cover,
            "author": news.author,
            "content": news.content,
            "excerpt": news.excerpt,
            "createdAt": news.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "isPinned": news.is_pinned,
            "profilePicture": news.profile_picture,
            "tag": news.tag,
            "type": news.type,
        }


# 创建服务实例
home_manage_service = HomeManageService()
