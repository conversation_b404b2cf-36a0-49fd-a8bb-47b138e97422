import random
import string
import hashlib
import redis
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from fastapi import HTTPException
from starlette.status import HTTP_400_BAD_REQUEST, HTTP_404_NOT_FOUND

from apps.app_user.model import User
from core.config import settings
from core.e import ErrorCode, ErrorMessage


class UserAuthService:
    """用户认证服务"""
    
    def __init__(self):
        # 初始化Redis连接
        self.redis_client = redis.Redis.from_url(settings.REDIS_URL, decode_responses=True)
    
    def generate_verification_code(self) -> str:
        """生成6位数字验证码"""
        return "".join(random.choices(string.digits, k=6))
    
    def hash_password(self, password: str) -> str:
        """使用MD5加密密码（保持与Flask版本一致）"""
        return hashlib.md5(password.encode('utf-8')).hexdigest().upper()
    
    async def send_email_verification_code(self, email: str) -> None:
        """发送邮箱验证码"""
        code = self.generate_verification_code()
        
        # TODO: 实际发送邮件的逻辑
        # 这里需要集成邮件发送服务
        print(f"验证码 {code} 已发送到邮箱 {email}")
        
        # 将验证码存入Redis，设置过期时间为5分钟
        self.redis_client.setex(f"email_verification:{email}", 300, code)
    
    def verify_email_verification_code(self, email: str, code: str) -> bool:
        """验证邮箱验证码"""
        stored_code = self.redis_client.get(f"email_verification:{email}")
        if stored_code and stored_code == code:
            # 验证成功后删除验证码
            self.redis_client.delete(f"email_verification:{email}")
            return True
        return False
    
    async def check_email_registered(self, db: AsyncSession, email: str) -> bool:
        """检查邮箱是否已注册"""
        result = await db.execute(select(User).filter(User.email == email))
        user = result.scalars().first()
        return user is not None
    
    async def register_user(self, db: AsyncSession, email: str, password: str, code: str) -> None:
        """用户注册"""
        # 验证验证码
        if not self.verify_email_verification_code(email, code):
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.VERIFICATION_CODE_ERROR}_{ErrorMessage.get(ErrorCode.VERIFICATION_CODE_ERROR)}"
            )
        
        # 检查邮箱是否已注册
        if await self.check_email_registered(db, email):
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.USER_EXIST}_{ErrorMessage.get(ErrorCode.USER_EXIST)}"
            )
        
        # 创建新用户
        hashed_password = self.hash_password(password)
        new_user = User(email=email, password=hashed_password)
        db.add(new_user)
        await db.commit()
    
    async def login_user(self, db: AsyncSession, email: str, password: str) -> tuple[int, str]:
        """用户登录"""
        hashed_password = self.hash_password(password)
        result = await db.execute(
            select(User).filter(User.email == email, User.password == hashed_password)
        )
        user = result.scalars().first()
        
        if not user:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.USER_PASSWORD_ERROR}_{ErrorMessage.get(ErrorCode.USER_PASSWORD_ERROR)}"
            )
        
        return user.id, user.email
    
    async def get_user_info(self, db: AsyncSession, user_id: int) -> dict:
        """获取用户信息"""
        result = await db.execute(select(User).filter(User.id == user_id))
        user = result.scalars().first()
        
        if not user:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"{ErrorCode.USER_NOT_FOUND}_{ErrorMessage.get(ErrorCode.USER_NOT_FOUND)}"
            )
        
        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "phone": user.phone,
            "status": user.status,
            "vipLevel": getattr(user, 'vip_level', 0),
        }


# 创建服务实例
user_auth_service = UserAuthService()
