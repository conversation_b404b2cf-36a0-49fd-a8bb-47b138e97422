from sqlalchemy import Column, String, <PERSON><PERSON><PERSON><PERSON>, Integer, BigInteger

from lib.security import verify_password, get_password_hash, generate_salt

from models.base import Base
from models.mixins import DateTimeModelMixin


DEFAULT_AVATAR_URL = "https://cdn.img.com/avatar.png"


class User(Base["User"], DateTimeModelMixin):
    __tablename__ = "users"

    id = Column(BigInteger, primary_key=True)
    username = Column(String(80), nullable=True)
    email = Column(String(120), unique=True, nullable=False, index=True)
    phone = Column(String(120), nullable=True)
    password = Column(String(128), nullable=False)
    vip_level = Column(String(80), nullable=False, default="1")
    status = Column(SmallInteger, nullable=False, default=1)
    current_plan_id = Column(Integer, nullable=True)
    current_subscription_id = Column(Integer, nullable=True)

    def check_password(self, password: str) -> bool:
        """
        检查密码是否相等（使用MD5哈希，与Flask版本兼容）

        Args:
            password (str): 密码

        Returns:
            bool: 是否相等
        """
        import hashlib
        hashed_password = hashlib.md5(password.encode('utf-8')).hexdigest().upper()
        return self.password == hashed_password

    def set_password(self, password: str) -> None:
        """
        设置密码（使用MD5哈希，与Flask版本兼容）

        Args:
            password (str): 新密码
        """
        import hashlib
        self.password = hashlib.md5(password.encode('utf-8')).hexdigest().upper()
