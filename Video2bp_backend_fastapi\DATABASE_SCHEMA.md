# 数据库表结构文档

## 概述

当前FastAPI项目定义了以下数据库表结构，这些表结构与原Flask项目保持兼容。

## 表结构详情

### 1. users 表 (用户表)

**表名**: `users`  
**模型**: `apps.app_user.model.User`

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | BigInteger | PRIMARY KEY | AUTO_INCREMENT | 用户ID |
| username | String(80) | NULL | NULL | 用户名 |
| email | String(120) | UNIQUE, NOT NULL, INDEX | - | 邮箱地址 |
| phone | String(120) | NULL | NULL | 手机号 |
| password | String(128) | NOT NULL | - | 密码(MD5加密) |
| vip_level | String(80) | NOT NULL | "1" | VIP等级 |
| status | SmallInteger | NOT NULL | 1 | 用户状态 |
| current_plan_id | Integer | NULL | NULL | 当前套餐ID |
| current_subscription_id | Integer | NULL | NULL | 当前订阅ID |
| created_at | DateTime | NOT NULL | NOW() | 创建时间 |
| updated_at | DateTime | NOT NULL | NOW() | 更新时间 |

**索引**:
- PRIMARY KEY: `id`
- UNIQUE INDEX: `email`

**特殊方法**:
- `check_password(password)`: 验证密码
- `set_password(password)`: 设置密码

---

### 2. user_videos 表 (用户视频表)

**表名**: `user_videos`  
**模型**: `apps.app_video_manage.model.UserVideos`

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | Integer | PRIMARY KEY | AUTO_INCREMENT | 视频ID |
| user_id | Integer | NOT NULL, INDEX | - | 用户ID |
| filename | String(128) | NULL | NULL | 视频文件名 |
| bp_filename | String(128) | NULL | NULL | 骨骼点文件名 |
| status | SmallInteger | NOT NULL | 1 | 状态 |
| created_at | DateTime | NOT NULL | NOW() | 创建时间 |
| updated_at | DateTime | NOT NULL | NOW() | 更新时间 |

**索引**:
- PRIMARY KEY: `id`
- INDEX: `user_id`

---

### 3. news 表 (新闻表)

**表名**: `news`  
**模型**: `apps.app_home_manage.model.News`

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | Integer | PRIMARY KEY | AUTO_INCREMENT | 新闻ID |
| name | String(255) | - | "" | 新闻名称 |
| type | Integer | NOT NULL | - | 新闻类型 |
| is_display | Boolean | - | True | 是否显示 |
| cover | String(500) | NOT NULL | - | 封面图片 |
| title | String(255) | NOT NULL | - | 标题 |
| author | String(100) | NOT NULL | "灵宇 AI 动捕" | 作者 |
| profile_picture | String(500) | NOT NULL | - | 作者头像 |
| excerpt | Text | NOT NULL | - | 摘要 |
| tag | String(100) | NULL | NULL | 标签 |
| content | LONGTEXT | NOT NULL | - | 内容 |
| is_pinned | Boolean | - | False | 是否置顶 |
| created_at | DateTime | NOT NULL | NOW() | 创建时间 |
| updated_at | DateTime | NOT NULL | NOW() | 更新时间 |

**索引**:
- PRIMARY KEY: `id`

---

### 4. backend 表 (后端表)

**表名**: `backend`  
**模型**: `apps.app_backend.model.Backend`

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | Integer | PRIMARY KEY | AUTO_INCREMENT | 后端ID |
| name | String(255) | - | - | 后端名称 |
| created_at | DateTime | NOT NULL | NOW() | 创建时间 |
| updated_at | DateTime | NOT NULL | NOW() | 更新时间 |
| deleted_at | DateTime | NULL | NULL | 删除时间(软删除) |

**索引**:
- PRIMARY KEY: `id`

**特殊功能**:
- 支持软删除 (SoftDeleteModelMixin)

---

## 公共字段说明

### DateTimeModelMixin
所有表都包含以下时间字段：
- `created_at`: 记录创建时间，自动设置为当前时间
- `updated_at`: 记录更新时间，每次更新时自动更新

### SoftDeleteModelMixin
部分表支持软删除功能：
- `deleted_at`: 删除时间，NULL表示未删除
- 提供 `remove()` 和 `remove_by()` 方法进行软删除

## 数据库配置

### 连接配置
```python
# 同步连接
DB_CONNECTION = "mysql+pymysql://user:password@host:port/database"

# 异步连接  
ASYNC_DB_CONNECTION = "mysql+aiomysql://user:password@host:port/database"
```

### 字符集
- 数据库字符集: `utf8mb4`
- 排序规则: `utf8mb4_unicode_ci`

## 迁移说明

### Alembic迁移
项目使用Alembic进行数据库迁移管理：

```bash
# 生成迁移文件
alembic revision --autogenerate -m "description"

# 执行迁移
alembic upgrade head

# 查看迁移历史
alembic history
```

### 与Flask项目兼容性
- 表名和字段名与原Flask项目完全一致
- 数据类型保持兼容
- 密码加密方式使用MD5（与Flask版本一致）
- 支持现有数据的无缝迁移

## 扩展建议

### 可能需要添加的表
1. **订单表** (orders)
   - 用户订单信息
   - 支付状态
   - 套餐关联

2. **套餐表** (plans)
   - 套餐详情
   - 价格信息
   - 功能限制

3. **用户订阅表** (subscriptions)
   - 用户订阅记录
   - 有效期管理
   - 自动续费

### 索引优化建议
1. `user_videos.user_id` - 已有索引
2. `news.type` - 建议添加索引
3. `news.is_display` - 建议添加索引
4. `users.status` - 建议添加索引

### 外键关系
当前模型未定义外键关系，建议在生产环境中添加：
1. `user_videos.user_id` → `users.id`
2. `users.current_plan_id` → `plans.id`
3. `users.current_subscription_id` → `subscriptions.id`
