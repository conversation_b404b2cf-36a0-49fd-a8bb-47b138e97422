#!/usr/bin/env python3
"""
演示lifespan生命周期管理的脚本
不依赖FastAPI，展示lifespan的概念和用法
"""

import asyncio
import os
from core.lifespan import startup_directories, startup_validation, shutdown_cleanup


async def demo_lifespan():
    """演示lifespan生命周期"""
    print("🚀 Demo: Starting application with lifespan management...")
    
    try:
        # 启动阶段
        print("\n========== 启动阶段 ==========")
        await startup_validation()
        await startup_directories()
        print("🎉 Application startup completed!")
        
        # 模拟应用运行
        print("\n========== 运行阶段 ==========")
        print("📱 Application is running...")
        print("📁 Check the created directories:")
        
        directories = ["static/videos", "static/bp", "static/videos/temp", "logs/app", "logs/celery"]
        for directory in directories:
            if os.path.exists(directory):
                print(f"   ✅ {directory}")
            else:
                print(f"   ❌ {directory}")
        
        # 模拟一些工作
        await asyncio.sleep(1)
        
        # 关闭阶段
        print("\n========== 关闭阶段 ==========")
        await shutdown_cleanup()
        print("👋 Application shutdown completed!")
        
    except Exception as e:
        print(f"💥 Error: {e}")


if __name__ == "__main__":
    print("🎯 Video2BP FastAPI Lifespan Demo")
    print("=" * 50)
    print("This demo shows how lifespan management works")
    print("without requiring FastAPI to be running.")
    print("=" * 50)
    
    asyncio.run(demo_lifespan())
    
    print("\n📝 Summary:")
    print("✅ Configuration validation")
    print("✅ Directory creation") 
    print("✅ Cleanup on shutdown")
    print("\n🎉 Lifespan demo completed successfully!")
    print("\nIn a real FastAPI app, this would happen automatically")
    print("when the server starts and stops.")
