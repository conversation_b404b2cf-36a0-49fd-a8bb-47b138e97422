from typing import Optional, List
from pydantic import BaseModel, Field
from schemas.base import BaseSchema
from schemas.response import StandardResponse


class VideoUploadResponse(BaseSchema):
    """视频上传响应"""
    filename: str = Field(..., description="文件名")


class VideoUploadResponseModel(StandardResponse):
    """视频上传响应模型"""
    data: Optional[VideoUploadResponse] = None


class VideoListRequest(BaseSchema):
    """视频列表请求"""
    startPage: int = Field(1, description="起始页", alias="start_page")
    pageSize: int = Field(10, description="页面大小", alias="page_size")


class VideoItem(BaseSchema):
    """视频项"""
    videoId: int = Field(..., description="视频ID", alias="video_id")
    filename: str = Field(..., description="文件名")
    bpFilename: Optional[str] = Field(None, description="骨骼点文件名", alias="bp_filename")
    createdAt: str = Field(..., description="创建时间", alias="created_at")
    updatedAt: str = Field(..., description="更新时间", alias="updated_at")
    status: int = Field(..., description="状态")


class VideoListResponse(BaseSchema):
    """视频列表响应"""
    total: int = Field(..., description="总数")
    videoList: List[VideoItem] = Field(..., description="视频列表", alias="video_list")


class VideoListResponseModel(StandardResponse):
    """视频列表响应模型"""
    data: Optional[VideoListResponse] = None


class ChunkUploadRequest(BaseSchema):
    """分片上传请求"""
    chunkNumber: int = Field(..., description="分片编号", alias="chunk_number")
    totalChunks: int = Field(..., description="总分片数", alias="total_chunks")
    fileId: str = Field(..., description="文件ID", alias="file_id")


class ChunkUploadResponse(BaseSchema):
    """分片上传响应"""
    progress: int = Field(..., description="进度")
    uploadedChunks: int = Field(..., description="已上传分片数", alias="uploaded_chunks")
    totalChunks: int = Field(..., description="总分片数", alias="total_chunks")


class ChunkUploadResponseModel(StandardResponse):
    """分片上传响应模型"""
    data: Optional[ChunkUploadResponse] = None


class MergeChunksRequest(BaseSchema):
    """合并分片请求"""
    fileId: str = Field(..., description="文件ID", alias="file_id")
    filename: str = Field(..., description="文件名")


class MergeChunksResponse(BaseSchema):
    """合并分片响应"""
    filename: str = Field(..., description="文件名")


class MergeChunksResponseModel(StandardResponse):
    """合并分片响应模型"""
    data: Optional[MergeChunksResponse] = None


class UploadStatusResponse(BaseSchema):
    """上传状态响应"""
    progress: int = Field(..., description="进度")
    uploadedChunks: int = Field(..., description="已上传分片数", alias="uploaded_chunks")
    totalChunks: int = Field(..., description="总分片数", alias="total_chunks")
    status: str = Field(..., description="状态")


class UploadStatusResponseModel(StandardResponse):
    """上传状态响应模型"""
    data: Optional[UploadStatusResponse] = None
