# Video2BP FastAPI Backend

## 🚀 快速开始

### 环境要求
- Python 3.8+
- MySQL 5.7+
- Redis 6.0+

### 安装依赖
```bash
# 创建虚拟环境
python -m venv fastapi_env
source fastapi_env/bin/activate  # Linux/Mac
# 或 fastapi_env\Scripts\activate  # Windows

# 安装依赖
pip install fastapi==0.100.1 pydantic==2.0.3 uvicorn==0.23.2
pip install -r requirements.txt
```

### 配置环境变量
创建 `.env` 文件：
```env
DB_CONNECTION=mysql+pymysql://user:password@localhost:3306/database
ASYNC_DB_CONNECTION=mysql+aiomysql://user:password@localhost:3306/database
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-secret-key
DEBUG=True
```

### 启动服务
```bash
python main.py
```

访问 http://localhost:8000/docs 查看API文档

## 📋 API模块

### 用户认证 (`/api/user/`)
- `POST /send_verification_code` - 发送邮箱验证码
- `POST /register` - 用户注册
- `POST /login` - 用户登录
- `GET /logout` - 用户登出
- `POST /info` - 获取用户信息

### 视频管理 (`/api/video/`)
- `POST /upload` - 视频上传
- `POST /list` - 视频列表查询
- `GET /download` - 视频下载
- `GET /bp/download` - 骨骼点文件下载
- `GET /download/all` - 打包下载
- `POST /upload_chunk` - 分片上传
- `POST /merge_chunks` - 合并分片

### 首页管理 (`/api/`)
- `GET /compony/info` - 公司信息
- `GET /combo/info` - 套餐信息
- `POST /news/list` - 新闻列表
- `GET /news/detail` - 新闻详情

### 其他
- `POST /api/order/create` - 创建订单
- `GET /api/monitor` - 系统监控

## 🗃️ 数据库表结构

### users (用户表)
- `id`, `email`, `username`, `password`, `phone`
- `vip_level`, `status`, `created_at`, `updated_at`

### user_videos (用户视频表)
- `id`, `user_id`, `filename`, `bp_filename`
- `status`, `created_at`, `updated_at`

### news (新闻表)
- `id`, `title`, `content`, `cover`, `author`
- `type`, `is_display`, `is_pinned`, `created_at`, `updated_at`

## 🔧 技术特性

- **异步支持**: 全面使用 async/await
- **类型安全**: Pydantic 模型验证
- **自动文档**: Swagger UI 和 ReDoc
- **Cookie认证**: 与Flask版本兼容的认证方式
- **文件上传**: 支持分片上传和合并
- **数据库**: SQLAlchemy 异步ORM
- **缓存**: Redis 集成

## 📁 项目结构

```
Video2bp_backend_fastapi/
├── apps/                   # 应用模块
│   ├── app_user_auth/     # 用户认证
│   ├── app_video_manage/  # 视频管理
│   ├── app_home_manage/   # 首页管理
│   ├── app_order_manage/  # 订单管理
│   └── app_monitor/       # 系统监控
├── api/routes/            # 路由配置
├── core/                  # 核心配置
├── lib/                   # 工具库
├── models/                # 数据库模型
├── schemas/               # Pydantic模型
├── main.py               # 应用入口
└── requirements.txt      # 依赖文件
```

