import os
import shutil
import uuid
import zipfile
from io import Bytes<PERSON>
from typing import Dict, Any, List
import redis
from fastapi import HTT<PERSON><PERSON>x<PERSON>, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import desc, func
from starlette.status import HTTP_400_BAD_REQUEST, HTTP_404_NOT_FOUND

from apps.app_video_manage.model import UserVideos
from core.config import settings
from core.e import ErrorCode, ErrorMessage


class VideoManageService:
    """视频管理服务"""
    
    def __init__(self):
        # 初始化Redis连接
        self.redis_client = redis.Redis.from_url(settings.REDIS_URL, decode_responses=True)
        
        # 视频文件扩展名
        self.video_extensions = {"mp4", "avi", "mov"}
        
        # 上传目录配置
        self.video_upload_folder = "static/videos"
        self.bp_upload_folder = "static/bp"
        
        # 确保目录存在
        os.makedirs(self.video_upload_folder, exist_ok=True)
        os.makedirs(self.bp_upload_folder, exist_ok=True)
    
    def allowed_file(self, filename: str) -> bool:
        """检查文件扩展名是否允许"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.video_extensions
    
    def generate_video_filename(self, original_filename: str) -> str:
        """生成唯一的视频文件名"""
        ext = original_filename.rsplit('.', 1)[1].lower()
        unique_id = str(uuid.uuid4()).replace('-', '')
        return f"{unique_id}.{ext}"
    
    async def upload_video(self, db: AsyncSession, user_id: int, file: UploadFile) -> Dict[str, Any]:
        """上传视频文件"""
        if not self.allowed_file(file.filename):
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.INVALID_PARAMS}_{ErrorMessage.get(ErrorCode.INVALID_PARAMS)}"
            )
        
        # 生成唯一文件名
        filename = self.generate_video_filename(file.filename)
        save_path = os.path.join(self.video_upload_folder, filename)
        
        # 保存文件
        with open(save_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 保存到数据库
        async with db.begin():
            new_user_video = UserVideos(user_id=user_id, filename=filename)
            db.add(new_user_video)
            await db.commit()
        
        return {"filename": filename}
    
    async def get_video_list(self, db: AsyncSession, user_id: int, start_page: int, page_size: int) -> Dict[str, Any]:
        """获取用户视频列表"""
        # 计算总数
        total_result = await db.execute(
            select(func.count()).select_from(UserVideos).filter(UserVideos.user_id == user_id)
        )
        total = total_result.scalar()
        
        # 获取分页数据
        offset = (start_page - 1) * page_size
        result = await db.execute(
            select(UserVideos)
            .filter(UserVideos.user_id == user_id)
            .order_by(desc(UserVideos.created_at))
            .offset(offset)
            .limit(page_size)
        )
        videos = result.scalars().all()
        
        video_list = []
        for video in videos:
            video_list.append({
                "videoId": video.id,
                "filename": video.filename,
                "bpFilename": video.bp_filename,
                "createdAt": video.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "updatedAt": video.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
                "status": video.status,
            })
        
        return {
            "total": total,
            "videoList": video_list
        }
    
    async def get_video_path(self, db: AsyncSession, user_id: int, video_id: int) -> str:
        """获取视频文件路径"""
        result = await db.execute(
            select(UserVideos).filter(
                UserVideos.id == video_id,
                UserVideos.user_id == user_id
            )
        )
        video = result.scalars().first()
        
        if not video:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"{ErrorCode.NOT_FOUND}_{ErrorMessage.get(ErrorCode.NOT_FOUND)}"
            )
        
        file_path = os.path.join(self.video_upload_folder, video.filename)
        
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"{ErrorCode.NOT_FOUND}_Video file not found"
            )
        
        return file_path
    
    async def get_bp_path(self, db: AsyncSession, user_id: int, video_id: int) -> str:
        """获取骨骼点文件路径"""
        result = await db.execute(
            select(UserVideos).filter(
                UserVideos.id == video_id,
                UserVideos.user_id == user_id
            )
        )
        video = result.scalars().first()
        
        if not video:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"{ErrorCode.NOT_FOUND}_{ErrorMessage.get(ErrorCode.NOT_FOUND)}"
            )
        
        # 生成骨骼点文件名
        bp_filename = f"{os.path.splitext(video.filename)[0]}_bp.json"
        bp_path = os.path.join(self.bp_upload_folder, bp_filename)
        
        if not os.path.exists(bp_path):
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"{ErrorCode.NOT_FOUND}_BP file not found"
            )
        
        return bp_path
    
    async def get_video_all(self, db: AsyncSession, user_id: int, video_id: int) -> BytesIO:
        """打包下载视频和骨骼点文件"""
        # 获取视频信息
        result = await db.execute(
            select(UserVideos).filter(
                UserVideos.id == video_id,
                UserVideos.user_id == user_id
            )
        )
        video = result.scalars().first()
        
        if not video:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"{ErrorCode.NOT_FOUND}_{ErrorMessage.get(ErrorCode.NOT_FOUND)}"
            )
        
        # 创建内存中的ZIP文件
        memory_file = BytesIO()
        
        with zipfile.ZipFile(memory_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            # 添加视频文件
            video_path = os.path.join(self.video_upload_folder, video.filename)
            if os.path.exists(video_path):
                zf.write(video_path, video.filename)
            
            # 添加骨骼点文件
            bp_filename = f"{os.path.splitext(video.filename)[0]}_bp.json"
            bp_path = os.path.join(self.bp_upload_folder, bp_filename)
            if os.path.exists(bp_path):
                zf.write(bp_path, bp_filename)
        
        memory_file.seek(0)
        return memory_file
    
    async def save_chunk(self, user_id: int, file_id: str, chunk: UploadFile, 
                        chunk_number: int, total_chunks: int) -> Dict[str, Any]:
        """保存分片文件"""
        # 创建临时目录
        temp_dir = os.path.join(self.video_upload_folder, "temp", file_id)
        os.makedirs(temp_dir, exist_ok=True)
        
        # 保存分块
        chunk_path = os.path.join(temp_dir, f"chunk_{chunk_number}")
        with open(chunk_path, "wb") as buffer:
            content = await chunk.read()
            buffer.write(content)
        
        # 更新Redis中的上传进度
        redis_key = f"upload:{user_id}:{file_id}"
        self.redis_client.hset(redis_key, str(chunk_number), "1")
        
        # 计算已完成分块数
        uploaded_chunks = len(self.redis_client.hkeys(redis_key))
        progress = int((uploaded_chunks / total_chunks) * 100)
        
        return {
            "progress": progress,
            "uploadedChunks": uploaded_chunks,
            "totalChunks": total_chunks
        }
    
    async def merge_chunks(self, db: AsyncSession, user_id: int, file_id: str, filename: str) -> Dict[str, Any]:
        """合并分片文件"""
        if not self.allowed_file(filename):
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.INVALID_PARAMS}_{ErrorMessage.get(ErrorCode.INVALID_PARAMS)}"
            )
        
        temp_dir = os.path.join(self.video_upload_folder, "temp", file_id)
        final_filename = self.generate_video_filename(filename)
        final_path = os.path.join(self.video_upload_folder, final_filename)
        
        # 合并所有分块
        with open(final_path, "wb") as outfile:
            chunk_files = sorted([f for f in os.listdir(temp_dir) if f.startswith("chunk_")],
                               key=lambda x: int(x.split("_")[1]))
            for chunk_file in chunk_files:
                chunk_path = os.path.join(temp_dir, chunk_file)
                with open(chunk_path, "rb") as infile:
                    outfile.write(infile.read())
        
        # 清理临时文件
        shutil.rmtree(temp_dir)
        
        # 清理Redis中的上传进度
        redis_key = f"upload:{user_id}:{file_id}"
        self.redis_client.delete(redis_key)
        
        # 保存到数据库
        async with db.begin():
            new_user_video = UserVideos(user_id=user_id, filename=final_filename)
            db.add(new_user_video)
            await db.commit()
        
        return {"filename": final_filename}
    
    async def get_upload_status(self, user_id: int, file_id: str) -> Dict[str, Any]:
        """获取上传状态"""
        redis_key = f"upload:{user_id}:{file_id}"
        uploaded_chunks = len(self.redis_client.hkeys(redis_key))
        
        # 这里需要从某个地方获取总分片数，可能需要在开始上传时存储
        # 暂时返回基本信息
        return {
            "uploadedChunks": uploaded_chunks,
            "status": "uploading" if uploaded_chunks > 0 else "not_started"
        }


# 创建服务实例
video_manage_service = VideoManageService()
