from fastapi import Depends, HTTPException, Request
from jose import JW<PERSON>rror, jwt
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from starlette.status import HTTP_401_UNAUTHORIZED

from apps.app_user.model import User
from core.config import settings
from core.e import ErrorCode, ErrorMessage
from db.database import get_async_db


async def get_current_user_from_cookie(
    request: Request,
    db: AsyncSession = Depends(get_async_db)
) -> User:
    """
    从Cookie中获取当前登录用户
    
    Args:
        request (Request): 请求对象
        db (AsyncSession): 数据库连接
        
    Returns:
        User: 当前登录用户
    """
    # 从Cookie中获取token
    token = request.cookies.get("auth_token")
    
    if not token:
        raise HTTPException(
            status_code=HTTP_401_UNAUTHORIZED,
            detail=f"{ErrorCode.USER_UNAUTHORIZED}_{ErrorMessage.get(ErrorCode.USER_UNAUTHORIZED)}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # 解码JWT token
        payload = jwt.decode(
            token, 
            key=settings.SECRET_KEY._value, 
            algorithms=[settings.JWT_ALGORITHM]
        )
        user_id = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(
                status_code=HTTP_401_UNAUTHORIZED,
                detail=f"{ErrorCode.USER_UNAUTHORIZED}_{ErrorMessage.get(ErrorCode.USER_UNAUTHORIZED)}",
                headers={"WWW-Authenticate": "Bearer"},
            )
            
    except JWTError:
        raise HTTPException(
            status_code=HTTP_401_UNAUTHORIZED,
            detail=f"{ErrorCode.USER_UNAUTHORIZED}_{ErrorMessage.get(ErrorCode.USER_UNAUTHORIZED)}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 查询用户
    async with db.begin():
        result = await db.execute(select(User).filter(User.id == user_id))
        db_user = result.scalars().first()
        
        if db_user is None:
            raise HTTPException(
                status_code=HTTP_401_UNAUTHORIZED,
                detail=f"{ErrorCode.USER_NOT_FOUND}_{ErrorMessage.get(ErrorCode.USER_NOT_FOUND)}",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    return db_user
