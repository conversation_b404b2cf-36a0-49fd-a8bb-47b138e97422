import time
from fastapi import APIRouter, Body, Depends, HTTPException, Response
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import HTTP_400_BAD_REQUEST

from apps.app_user_auth.schema import (
    SendVerificationCodeRequest,
    SendVerificationCodeResponseModel,
    UserRegisterRequest,
    UserRegisterResponseModel,
    UserLoginRequest,
    UserLoginResponseModel,
    UserLogoutResponseModel,
    UserInfoRequest,
    UserInfoResponseModel,
)
from apps.app_user_auth.service import user_auth_service
from core.config import settings
from core.e import ErrorCode, ErrorMessage
from db.database import get_async_db
from lib.jwt import create_access_token, get_current_user
from apps.app_user.model import User
from schemas.response import StandardResponse


router = APIRouter()


@router.post(
    "/send_verification_code",
    name="发送邮箱验证码",
    response_model=SendVerificationCodeResponseModel,
)
async def send_verification_code(
    request: SendVerificationCodeRequest = Body(...),
):
    """发送邮箱验证码"""
    try:
        await user_auth_service.send_email_verification_code(request.email)
        return SendVerificationCodeResponseModel()
    except Exception as e:
        return SendVerificationCodeResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.post(
    "/register",
    name="用户注册",
    response_model=UserRegisterResponseModel,
)
async def register(
    request: UserRegisterRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
):
    """用户注册"""
    try:
        await user_auth_service.register_user(
            db, request.email, request.password, request.code
        )
        return UserRegisterResponseModel()
    except HTTPException as e:
        return UserRegisterResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return UserRegisterResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.post(
    "/login",
    name="用户登录",
    response_model=UserLoginResponseModel,
)
async def login(
    request: UserLoginRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
):
    """用户登录"""
    try:
        user_id, email = await user_auth_service.login_user(
            db, request.email, request.password
        )
        
        # 创建JWT token
        token = create_access_token(user_id)
        
        # 创建响应
        response_data = UserLoginResponseModel()
        response = response_data.to_json()
        
        # 设置Cookie（保持与Flask版本一致）
        # 1. 非HttpOnly Cookie，前端可读取
        response.set_cookie(
            "auth_status",
            "authenticated",
            max_age=7 * 24 * 60 * 60,  # 7天
            httponly=False,
            secure=not settings.DEBUG,  # 生产环境使用HTTPS
            samesite="strict"
        )
        
        # 2. HttpOnly Cookie，前端不可读取，用于API认证
        response.set_cookie(
            "auth_token",
            token,
            max_age=7 * 24 * 60 * 60,  # 7天
            httponly=True,
            secure=not settings.DEBUG,
            samesite="strict"
        )
        
        return response
        
    except HTTPException as e:
        return UserLoginResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return UserLoginResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.get(
    "/logout",
    name="用户登出",
    response_model=UserLogoutResponseModel,
)
async def logout():
    """用户登出"""
    response_data = UserLogoutResponseModel()
    response = response_data.to_json()
    
    # 删除Cookie
    response.delete_cookie("auth_status")
    response.delete_cookie("auth_token")
    
    return response


@router.post(
    "/info",
    name="获取用户信息",
    response_model=UserInfoResponseModel,
)
async def user_info(
    request: UserInfoRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """获取用户信息"""
    try:
        user_data = await user_auth_service.get_user_info(db, request.userId)
        return UserInfoResponseModel(data=user_data)
    except HTTPException as e:
        return UserInfoResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return UserInfoResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)
