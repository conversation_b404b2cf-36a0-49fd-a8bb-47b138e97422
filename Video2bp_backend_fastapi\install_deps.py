#!/usr/bin/env python3
"""
依赖安装脚本
逐步安装依赖包，避免编码问题
"""

import subprocess
import sys


def run_pip_install(package):
    """安装单个包"""
    try:
        print(f"Installing {package}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print(f"✅ Successfully installed {package}")
            return True
        else:
            print(f"❌ Failed to install {package}")
            print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error installing {package}: {e}")
        return False


def main():
    """主函数"""
    print("🚀 Installing FastAPI dependencies...")
    
    # 核心依赖包列表
    packages = [
        "fastapi==0.100.1",
        "pydantic==2.0.3", 
        "uvicorn==0.23.2",
        "python-multipart==0.0.6",
        "sqlalchemy==2.0.20",
        "aiomysql==0.2.0",
        "alembic==1.11.3",
        "pymysql==1.1.0",
        "python-jose[cryptography]==3.3.0",
        "passlib[bcrypt]==1.7.4",
        "bcrypt==4.0.1",
        "redis==4.6.0",
        "email-validator==2.0.0",
        "loguru==0.7.0",
        "requests==2.31.0",
        "databases==0.7.0"
    ]
    
    failed_packages = []
    
    for package in packages:
        if not run_pip_install(package):
            failed_packages.append(package)
    
    print("\n" + "="*50)
    if failed_packages:
        print(f"❌ Failed to install {len(failed_packages)} packages:")
        for pkg in failed_packages:
            print(f"   - {pkg}")
        print("\nYou can try installing them manually:")
        for pkg in failed_packages:
            print(f"pip install {pkg}")
    else:
        print("🎉 All packages installed successfully!")
        print("\nNext steps:")
        print("1. Run: python minimal_test.py")
        print("2. If successful, run: python main.py")
    
    return len(failed_packages) == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
