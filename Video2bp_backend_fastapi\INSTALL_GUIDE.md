# 安装指南 - 解决编码问题

## 问题说明

遇到的错误是由于requirements.txt文件编码问题导致的：
```
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 22: illegal multibyte sequence
```

## 解决方案

### 方法1：手动逐个安装（推荐）

在激活的虚拟环境中，逐个运行以下命令：

```bash
# 核心框架
pip install fastapi==0.100.1
pip install pydantic==2.0.3
pip install uvicorn==0.23.2

# 基础依赖
pip install python-multipart==0.0.6
pip install requests==2.31.0

# 数据库相关
pip install sqlalchemy==2.0.20
pip install aiomysql==0.2.0
pip install alembic==1.11.3
pip install pymysql==1.1.0

# 认证和安全
pip install "python-jose[cryptography]==3.3.0"
pip install "passlib[bcrypt]==1.7.4"
pip install bcrypt==4.0.1

# 其他依赖
pip install redis==4.6.0
pip install email-validator==2.0.0
pip install loguru==0.7.0
pip install databases==0.7.0
```

### 方法2：使用Python脚本安装

运行提供的安装脚本：
```bash
python install_deps.py
```

### 方法3：最小化安装（快速测试）

如果只想快速测试，可以只安装核心依赖：
```bash
pip install fastapi==0.100.1 pydantic==2.0.3 uvicorn==0.23.2 python-multipart==0.0.6
```

然后运行：
```bash
python minimal_test.py
```

## 验证安装

安装完成后，运行以下命令验证：

```bash
# 检查已安装的包
pip list | findstr -i "fastapi pydantic uvicorn"

# 测试导入
python -c "import fastapi, pydantic, uvicorn; print('✅ Core packages imported successfully')"

# 运行最小测试
python minimal_test.py
```

## 常见问题

### Q: 安装过程中出现网络错误
A: 可以使用国内镜像源：
```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple fastapi==0.100.1
```

### Q: 某些包安装失败
A: 可以跳过可选依赖，只安装核心包：
- fastapi
- pydantic  
- uvicorn
- python-multipart

### Q: 如何确认虚拟环境已激活
A: 命令行前缀应该显示 `(fastapi_env)`，或运行：
```bash
python -c "import sys; print(sys.prefix)"
```

## 下一步

安装成功后：

1. **测试基本功能**：
   ```bash
   python minimal_test.py
   ```

2. **如果测试成功，启动完整应用**：
   ```bash
   python main.py
   ```

3. **访问API文档**：
   - http://localhost:8000/docs
   - http://localhost:8000/redoc

## 故障排除

如果仍有问题：

1. **重新创建虚拟环境**：
   ```bash
   deactivate
   rmdir /s fastapi_env
   python -m venv fastapi_env
   fastapi_env\Scripts\activate
   ```

2. **升级pip**：
   ```bash
   python -m pip install --upgrade pip
   ```

3. **检查Python版本**：
   ```bash
   python --version
   ```
   确保使用Python 3.8+

## 成功标志

当看到以下输出时，说明安装成功：
```
✅ FastAPI imports successful
✅ FastAPI app created successfully
🚀 Starting FastAPI server...
INFO:     Started server process
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://127.0.0.1:8000
```
