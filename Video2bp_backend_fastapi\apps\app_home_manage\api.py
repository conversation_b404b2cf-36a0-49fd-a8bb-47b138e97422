from fastapi import APIRouter, Body, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import HTTP_400_BAD_REQUEST

from apps.app_home_manage.schema import (
    CompanyInfoResponseModel,
    ComboInfoResponseModel,
    NewsListRequest,
    NewsListResponseModel,
    NewsDetailResponseModel,
)
from apps.app_home_manage.service import home_manage_service
from core.e import ErrorCode, ErrorMessage
from db.database import get_async_db
from schemas.response import StandardResponse


router = APIRouter()


@router.get(
    "/compony/info",
    name="公司信息",
    response_model=CompanyInfoResponseModel,
)
async def company_info():
    """获取公司信息"""
    try:
        result = home_manage_service.get_company_info()
        return CompanyInfoResponseModel(data=result)
    except Exception as e:
        return CompanyInfoResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.get(
    "/combo/info",
    name="套餐信息",
    response_model=ComboInfoResponseModel,
)
async def combo_info():
    """获取套餐信息"""
    try:
        result = home_manage_service.get_combo_info()
        return ComboInfoResponseModel(data=result)
    except Exception as e:
        return ComboInfoResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.post(
    "/news/list",
    name="资讯信息",
    response_model=NewsListResponseModel,
)
async def news_list(
    request: NewsListRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
):
    """获取新闻列表"""
    try:
        result = await home_manage_service.get_news_list(
            db, request.page, request.size, request.type
        )
        return NewsListResponseModel(data=result)
    except Exception as e:
        return NewsListResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.get(
    "/news/detail",
    name="资讯详情信息",
    response_model=NewsDetailResponseModel,
)
async def news_detail(
    id: int = Query(..., description="新闻ID"),
    db: AsyncSession = Depends(get_async_db),
):
    """获取新闻详情"""
    try:
        result = await home_manage_service.get_news_detail(db, id)
        return NewsDetailResponseModel(data=result)
    except HTTPException as e:
        return NewsDetailResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return NewsDetailResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)
