#!/usr/bin/env python3
"""
简单的FastAPI测试启动脚本
"""

from fastapi import FastAPI
from fastapi.responses import JSONResponse

app = FastAPI(title="Video2BP FastAPI", version="1.0.0")

@app.get("/")
async def root():
    return {"message": "Video2BP FastAPI is running"}

@app.get("/api/monitor")
async def monitor():
    return {"code": 0, "message": "System is running", "data": {}}

@app.get("/api/compony/info")
async def company_info():
    return {
        "code": 0,
        "message": "success",
        "data": {
            "name": "灵宇科技",
            "description": "专业的AI动作捕捉解决方案提供商",
            "address": "北京市海淀区中关村科技园",
            "phone": "************",
            "email": "<EMAIL>"
        }
    }

@app.get("/api/combo/info")
async def combo_info():
    return {
        "code": 0,
        "message": "success",
        "data": {
            "combos": [
                {
                    "id": 1,
                    "name": "基础版",
                    "price": 99.0,
                    "description": "适合个人用户的基础功能",
                    "features": ["基础动作捕捉", "标准输出格式", "在线支持"]
                },
                {
                    "id": 2,
                    "name": "专业版",
                    "price": 299.0,
                    "description": "适合专业用户的高级功能",
                    "features": ["高精度动作捕捉", "多种输出格式", "优先技术支持", "批量处理"]
                }
            ]
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
