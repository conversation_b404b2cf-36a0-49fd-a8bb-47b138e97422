from starlette.status import (
    HTTP_200_OK,
    HTTP_404_NOT_FOUND,
)

from core.e import <PERSON>rrorCode, ErrorMessage
from schemas.response import StandardResponse

# common

NOT_FOUND = {
    "description": "backend 不存在.",
    "model": StandardResponse,
    "content": {
        "application/json": {
            "example": {
                "code": ErrorCode.NOT_FOUND,
                "message": ErrorMessage.get(ErrorCode.NOT_FOUND),
            }
        }
    },
}

# ======================>>>>>>>>>>>>>>>>>>>>>> get_backend

get_backends_responses = {
    HTTP_200_OK: {
        "description": "获取 backend 列表成功",
        "content": {
            "application/json": {
                "example": {
                    "code": 0,
                    "data": {
                        "list": [
                            {
                                "id": 1,
                                "name": "test1",
                            },
                            {
                                "id": 2,
                                "name": "test02",
                            }
                        ],
                        "count": 2,
                        "total": 5,
                        "page": 1,
                        "size": 2
                    },
                    "message": "",
                }
            }
        },
    }
}

# ======================>>>>>>>>>>>>>>>>>>>>>> create_backend

create_backend_request = {
    "创建 backend": {
        "description": "创建时需要输入 <u>**name**</u>.",
        "value": {
            "name": "new_name",
        }
    }
}

create_backend_responses = {
    HTTP_200_OK: {
        "description": "创建 backend 成功",
        "content": {
            "application/json": {
                "example": {
                    "code": 0,
                    "data": {
                        "id": 1,
                        "name": "new_name",
                    },
                    "message": "",
                }
            }
        }
    }
}

# ======================>>>>>>>>>>>>>>>>>>>>>> patch_backends

patch_backends_responses = {
    HTTP_200_OK: {
        "description": "批量更新 backend 成功",
        "content": {
            "application/json": {
                "example": {
                    "code": 0,
                    "data": {
                        "ids": [1, 2],
                        "name": "new_name",
                    },
                    "message": "",
                }
            }
        }
    }
}

patch_backends_request = {
    "批量更新 backend name": {
        "description": "批量更新 backend，返回更新成功的 backend id 和更新条目",
        "value": {
            "ids": [1, 2, 3],
            "name": "new_name",
        },
    }
}

# ======================>>>>>>>>>>>>>>>>>>>>>> delete_backends

delete_backends_responses = {
    HTTP_200_OK: {
        "description": "批量删除 backend 成功",
        "content": {
            "application/json": {
                "example": {
                    "code": 0,
                    "data": {
                        "ids": [1, 2],
                    },
                    "message": "",
                }
            }
        }
    }
}

delete_backends_request = {
    "example": [1, 2, 3],
}

# ======================>>>>>>>>>>>>>>>>>>>>>> get_backend_by_id

get_backend_by_id_responses = {
    HTTP_200_OK: {
        "description": "获取 backend 信息成功.",
        "content": {
            "application/json": {
                "example": {
                    "code": 0,
                    "message": "",
                    "data": {
                        "id": 1,
                        "name": "backend",
                        "created_at": "2023-07-03 08:03:03",
                        "updated_at": "2023-07-03 08:03:03",
                    },
                }
            }
        },
    },
    HTTP_404_NOT_FOUND: NOT_FOUND,
}

# ======================>>>>>>>>>>>>>>>>>>>>>> update_backend_by_id

update_backend_by_id_responses = {
    HTTP_200_OK: {
        "description": "更改 backend 成功",
        "content": {
            "application/json": {
                "example": {
                    "code": 0,
                    "data": {
                        "id": 1,
                        "name": "new_name",
                    },
                    "message": "",
                },
            },
        },
    },
    HTTP_404_NOT_FOUND: NOT_FOUND,
}

update_backend_by_id_request = {
    "更新 name": {
        "description": "设置 `name` 为新值.",
        "value": {
            "name": "new_name",
        },
    },
}

# ======================>>>>>>>>>>>>>>>>>>>>>> delete_backend_by_id

delete_backend_by_id_responses = {
    HTTP_200_OK: {
        "description": "注销 backend 成功",
        "content": {
            "application/json": {
                "example": {
                    "code": 0,
                    "data": {
                        "id": 1,
                    },
                    "message": ""
                },
            },
        },
    },
    HTTP_404_NOT_FOUND: NOT_FOUND,
}
