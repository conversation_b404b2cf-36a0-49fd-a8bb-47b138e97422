import datetime
from typing import List, Literal

from fastapi import Query
from pydantic import Field, field_validator
from apps.app_backend.field import BackendFields
from apps.app_backend.model import Backend
from models.base import get_model_fields_from_objects
from schemas.base import BaseSchema, QuerySchema
from schemas.response import PaginationResponse, StandardResponse


# ======================>>>>>>>>>>>>>>>>>>>>>> get_it_demos


class BackendListQueryRequest(QuerySchema):
    """获取 backend 列表 查询 请求"""

    order_by: Literal["id", "created_at"] = Query(
        Backend.id.name, description="排序字段. eg: id created_at"
    )

    @field_validator("order_by")
    def validate_order_by(cls, v: str) -> str:
        order_fields = get_model_fields_from_objects(Backend, [Backend.id, Backend.created_at])
        if v not in order_fields:
            raise ValueError(f"order_by: {v} not in {order_fields}")
        return v


class BackendListResponse(BaseSchema):
    """获取 backend 列表 响应"""

    id: int = BackendFields.id
    name: str = BackendFields.name


class BackendListResponseModel(StandardResponse):
    """获取 backend 列表 响应 Model"""

    data: PaginationResponse[BackendListResponse]


# ======================>>>>>>>>>>>>>>>>>>>>>> create_backend


class BackendCreateRequest(BaseSchema):
    """创建 backend 请求"""

    name: str = BackendFields.name


class BackendCreateResponse(BaseSchema):
    """创建 backend 响应"""

    id: int = BackendFields.id
    name: str = BackendFields.name


class BackendCreateResponseModel(StandardResponse):
    """创建 backend 响应 Model"""

    data: BackendCreateResponse | None = None


# ======================>>>>>>>>>>>>>>>>>>>>>> patch_backends


class BackendsPatchRequest(BaseSchema):
    """批量更新 backend 请求"""

    ids: List[int] = Field(..., description="backend id 列表")
    name: str = BackendFields.name


class BackendsPatchResponse(BaseSchema):
    """批量更新 backend 响应"""

    ids: List[int] = Field(..., description="backend id 列表")
    name: str = BackendFields.name


class BackendsPatchResponseModel(StandardResponse):
    """批量更新 backend 响应 Model"""

    data: BackendsPatchResponse | None = None


# ======================>>>>>>>>>>>>>>>>>>>>>> delete_backends


class BackendsDeleteResponse(BaseSchema):
    """批量删除 backend 响应"""

    ids: List[int] = Field(..., description="backend id 列表")


class BackendsDeleteResponseModel(StandardResponse):
    """批量删除 backend 响应 Model"""

    data: BackendsDeleteResponse


# ======================>>>>>>>>>>>>>>>>>>>>>> get_backend_by_id


class BackendInfoResponse(BaseSchema):
    """获取 backend by id 响应"""

    id: int = BackendFields.id
    name: str = BackendFields.name
    created_at: datetime.datetime = BackendFields.created_at
    updated_at: datetime.datetime = BackendFields.updated_at


class BackendInfoResponseModel(StandardResponse):
    """获取 backend by id 响应 Model"""

    data: BackendInfoResponse | None = None


# ======================>>>>>>>>>>>>>>>>>>>>>> update_backend_by_id


class BackendUpdateRequest(BaseSchema):
    """更新 backend by id 请求"""

    name: str = BackendFields.name


class BackendUpdateResponse(BaseSchema):
    """更新 backend by id 响应"""

    id: int = BackendFields.id
    name: str = BackendFields.name


class BackendUpdateResponseModel(StandardResponse):
    """更新 backend by id 响应 Model"""

    data: BackendUpdateResponse | None = None


# ======================>>>>>>>>>>>>>>>>>>>>>> delete_backend_by_id


class BackendDeleteResponse(BaseSchema):
    """删除 backend by id 响应"""

    id: int = BackendFields.id


class BackendDeleteResponseModel(StandardResponse):
    """删除 backend by id 响应 Model"""

    data: BackendDeleteResponse | None = None
